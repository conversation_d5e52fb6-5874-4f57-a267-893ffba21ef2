/* Modern, responsive styles for Fazenda Alberto Vidal site */

:root{
  --green-900:#153918;
  --green-700:#2f8b3a;
  --green-400:#62b05a;
  --accent:#f6a623;
  --bg:#f6fff2;
  --card:#ffffff;
  --muted:#6b6b6b;
  --glass:rgba(255,255,255,0.75);
  --maxw:1200px;
  --shadow: 0 10px 30px rgba(17,24,39,0.08);
  --radius:12px;
  --gap:1rem;
}

*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family:Inter, system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial;
  background:linear-gradient(180deg, #f7fff6 0%, #eafaf0 60%);
  color:#102611;
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  line-height:1.45;
}

.container{max-width:var(--maxw);margin:0 auto;padding:1rem}

/* Header */
.site-header{
  background:linear-gradient(90deg,var(--card),#f8fff7);
  position:sticky;top:0;z-index:60;box-shadow:var(--shadow);
  border-bottom:1px solid rgba(46,139,58,0.04);
}
.header-inner{display:flex;align-items:center;justify-content:space-between;padding:0.9rem 1rem}
.brand{display:flex;gap:0.8rem;align-items:center}
.logo{width:64px;height:64px;border-radius:10px;object-fit:cover;box-shadow:0 8px 30px rgba(37,56,38,0.06)}
.brand h1{margin:0;font-size:1.05rem}
.sub{margin:0;font-size:0.8rem;color:var(--muted)}

.nav{display:flex;gap:0.6rem;align-items:center}
.nav a{color:var(--green-900);text-decoration:none;padding:0.45rem 0.7rem;border-radius:10px;transition:all .18s}
.nav a:hover{background:rgba(46,139,58,0.08)}
.cart-btn{background:var(--green-700);color:white;padding:0.45rem 0.8rem;border-radius:10px;border:0;cursor:pointer;box-shadow:0 6px 18px rgba(46,139,58,0.08)}

/* Hero */
.hero{padding:2.2rem 0;background:linear-gradient(120deg,#f3fff0,#eaffdf);overflow:hidden}
.hero-grid{display:grid;grid-template-columns:1fr 520px;gap:2rem;align-items:center}
.hero-text h2{font-size:2rem;margin:0 0 0.6rem;color:var(--green-900)}
.hero-text p{margin:0 0 1rem;color:#2b492f}
.hero-actions{display:flex;gap:0.6rem;margin-bottom:1rem}
.btn{padding:0.65rem 1rem;border-radius:10px;cursor:pointer;border:1px solid transparent;text-decoration:none}
.btn-primary{background:var(--green-700);color:#fff;box-shadow:0 8px 20px rgba(46,139,58,0.06)}
.btn-outline{background:transparent;color:var(--green-700);border:1px solid rgba(46,139,58,0.12)}

/* Loyalty card */
.loyalty{margin-top:1rem;border-left:4px solid var(--green-700);padding:0.8rem;background:linear-gradient(180deg,rgba(98,163,93,0.04),transparent);border-radius:10px}
.loyalty-form{display:flex;flex-wrap:wrap;gap:0.6rem}
.input-field{padding:0.7rem;border-radius:10px;border:1px solid #e6f3e4;background:#fff;flex:1;outline:none;transition:box-shadow .14s}
.input-field:focus{box-shadow:0 6px 18px rgba(46,139,58,0.06);border-color:var(--green-700)}

/* Gallery */
.gallery-grid{display:grid;grid-template-columns:repeat(3,1fr);gap:0.6rem;margin-top:0.8rem}
.gallery-grid img{width:100%;height:160px;object-fit:cover;border-radius:8px;box-shadow:var(--shadow);transition:transform .25s}
.gallery-grid img:hover{transform:scale(1.03)}

/* Partners */
.partners-grid{display:flex;gap:1rem;flex-wrap:wrap;align-items:center;justify-content:space-between;padding:1rem}
.partner{display:flex;gap:0.8rem;align-items:center;padding:0.6rem;border-radius:10px;background:linear-gradient(180deg,#fff,#f6fff7);box-shadow:0 6px 20px rgba(36,60,32,0.04)}
.partner img{width:64px;height:48px;object-fit:cover;border-radius:8px}
.partner span{font-weight:600;color:var(--green-900)}

/* Stats */
.stats-grid{display:flex;gap:1rem;margin-top:0.8rem}
.stat{flex:1;text-align:center;padding:1.2rem;border-radius:12px;background:var(--card);box-shadow:var(--shadow)}
.stat .big{font-size:1.8rem;color:var(--green-700);margin:0.6rem 0;font-weight:700}

/* Products */
.products-grid{
  display:grid;
  grid-template-columns:repeat(auto-fit,minmax(240px,1fr));
  gap:var(--gap);
  margin-top:1rem;
}
.product{
  background:linear-gradient(180deg,#fff,#f8fff6);
  padding:0.9rem;border-radius:14px;box-shadow:0 10px 30px rgba(17,24,39,0.04);
  display:flex;flex-direction:column;gap:0.6rem;transition:transform .18s;
}
.product:hover{transform:translateY(-6px)}
.product img{width:100%;height:160px;object-fit:cover;border-radius:10px}
.product h5{margin:0;font-size:1rem}
.product .meta{display:flex;justify-content:space-between;align-items:center;gap:0.4rem}
.price{font-weight:800;color:var(--green-700)}
.product-actions{display:flex;justify-content:space-between;align-items:center;margin-top:auto}
.btn-sm{padding:0.4rem 0.6rem;border-radius:8px;font-size:0.95rem}

/* Cart drawer */
.cart-drawer{
  position:fixed;right:-440px;top:0;height:100%;width:420px;background:linear-gradient(180deg,#fff,#f8fff7);
  box-shadow:-30px 0 50px rgba(17,24,39,0.08);transition:right .32s;z-index:120;padding:1rem;display:flex;flex-direction:column;border-left:1px solid rgba(46,139,58,0.04)
}
.cart-drawer.open{right:0}
.cart-header{display:flex;justify-content:space-between;align-items:center;gap:0.6rem}
.btn-close{background:transparent;border:0;font-size:1.6rem;cursor:pointer}
.cart-items{flex:1;overflow:auto;margin-top:0.8rem;padding-right:6px}
.cart-item{display:flex;gap:0.6rem;align-items:center;padding:0.6rem;border-radius:10px;border-bottom:1px solid #eef7ee}
.cart-item img{width:72px;height:60px;object-fit:cover;border-radius:8px}
.cart-item .info{flex:1}
.qty{display:flex;gap:0.4rem;align-items:center;margin-top:6px}
.qty button{width:30px;height:30px;border-radius:8px;border:1px solid #e6f3e4;background:#fff;cursor:pointer}
.remove-link{background:transparent;border:0;color:#c43;border-radius:8px;cursor:pointer;padding:0 6px}

/* Checkout area inside drawer */
.checkout-area{margin-top:0.6rem;padding:0.8rem;border-radius:10px;background:linear-gradient(180deg,#fff,#fbfff9);box-shadow:0 8px 30px rgba(17,24,39,0.04)}
.checkout-area label{display:block;font-size:0.85rem;margin:0.45rem 0 0.2rem;color:var(--green-900)}
.checkout-area input[type="text"], .checkout-area input[type="email"], .checkout-area input[type="tel"], .checkout-area input[type="file"], .checkout-area textarea{width:100%;padding:0.6rem;border-radius:8px;border:1px solid #e6f3e4}
.checkout-actions{display:flex;gap:0.6rem;justify-content:flex-end;margin-top:0.6rem}

/* File preview & receipt */
#filePreview img{max-width:100%;border-radius:8px;box-shadow:0 8px 20px rgba(17,24,39,0.06)}
.receipt{padding:0.8rem;border-radius:10px;background:linear-gradient(180deg,#fff,#f8fff7);box-shadow:var(--shadow);margin-top:0.6rem}

/* Footer */
.site-footer{background:var(--green-900);color:#fff;padding:1rem;margin-top:2rem;border-radius:12px 12px 0 0}
.site-footer .muted{opacity:0.95;font-size:0.9rem}

/* Responsive */
@media (max-width:1100px){
  .hero-grid{grid-template-columns:1fr 380px}
}
@media (max-width:720px){
  .nav{display:none}
  .hero-grid{grid-template-columns:1fr}
  .products-grid{grid-template-columns:1fr}
  .logo{width:48px;height:48px}
  .cart-drawer{width:100%;right:-100%}
}

/* Animations */
@keyframes pop{from{transform:translateY(8px) scale(.98);opacity:0}to{transform:none;opacity:1}}
.card, .product, .stat {animation:pop .45s ease both}
#year{opacity:.9}
.hero-img{width:450px;height:450px}