<!DOCTYPE html>
<html lang="pt">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Loja — <PERSON>az<PERSON><PERSON></title>
  <link rel="icon" href="img/logo.jpeg" />
  <link rel="stylesheet" href="styles/main.css" />
</head>
<body>
  <header class="site-header">
    <div class="container header-inner">
      <div class="brand">
        <img src="img/logo.jpeg" alt="Logo" class="logo"/>
        <div><h1>Loja AV</h1><p class="sub">Animais, sementes e serviços</p></div>
      </div>
      <nav class="nav">
        <a href="index.html">Início</a>
        <a href="sobre.html">Sobre</a>
        <a href="loja.html">Loja</a>
        <a href="contacto.html">Contacto</a>
        <button id="cartBtn" class="cart-btn">Carrinho (<span id="cartCount">0</span>)</button>
      </nav>
    </div>
  </header>

  <main class="container section">
    <h2 class="section-title">Produtos & Animais</h2>
    <div id="products" class="products-grid"></div>
  </main>

  <aside id="cartDrawer" class="cart-drawer" aria-hidden="true" tabindex="-1">
    <div class="cart-header">
      <h4>Carrinho</h4>
      <button id="closeCart" class="btn-close" aria-label="Fechar">×</button>
    </div>

    <div id="cartItems" class="cart-items">
      <!-- itens gerados dinamicamente; as imagens dos produtos virão de scripts/main.js (img/...) -->
    </div>

    <div class="checkout-area" id="checkoutArea" style="display:none">
      <h4>Finalizar Compra</h4>
      <form id="checkoutForm">
        <label for="buyerName">Nome completo</label>
        <input class="input-field" type="text" id="buyerName" name="buyerName" required />

        <label for="buyerEmail">Email</label>
        <input class="input-field" type="email" id="buyerEmail" name="buyerEmail" required />

        <label for="buyerTel">Telefone</label>
        <input class="input-field" type="tel" id="buyerTel" name="buyerTel" />

        <label for="buyerAddress">Endereço / Observações</label>
        <input class="input-field" type="text" id="buyerAddress" name="buyerAddress" />

        <label>IBAN para transferência</label>
        <div class="card" style="padding:0.6rem;margin-bottom:0.6rem;background:linear-gradient(180deg,#fff,#fbfff9)">
          <strong>IBAN:</strong> AO06 0000 0000 0000 0000 0000 (exemplo) <br/>
          <small class="muted">Transfere o valor para o IBAN acima e anexa o comprovativo (imagem). Depois clique em "Concluir e Emitir PDF".</small>
        </div>

        <label for="comprovativo">Anexar comprovativo (imagem preferível)</label>
        <input class="input-field" type="file" id="comprovativo" name="comprovativo" accept="image/*" />

        <div id="filePreview" style="display:none;margin-top:0.6rem"></div>

        <div class="checkout-actions">
          <button type="button" id="cancelCheckout" class="btn btn-outline">Cancelar</button>
          <button type="submit" class="btn btn-primary">Concluir e Emitir PDF</button>
        </div>
      </form>
    </div>

    <div class="cart-footer">
      <div class="totals">
        <div>Itens: <span id="cartQty">0</span></div>
        <div>Total: <strong id="cartTotal">AOA 0</strong></div>
      </div>
      <div class="cart-actions">
        <button id="clearCart" class="btn btn-outline">Limpar</button>
        <!-- navegar para a página de checkout em vez de abrir formulário no drawer -->
        <a href="checkout.html" id="gotoCheckout" class="btn btn-primary">Finalizar Compra</a>
      </div>
    </div>
  </aside>

  <footer class="site-footer">
    <div class="container">
      <p>© <span id="year"></span> Fazenda Alberto Vidal Agropecuária</p>
    </div>
  </footer>

  <!-- adicionar jsPDF CDN antes do main.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="scripts/main.js" defer></script>
</body>
</html>