/* FunÃ§Ãµes e lÃ³gica da loja, carrinho e animaÃ§Ãµes */
const STORAGE_KEY = 'av_fazenda_cart_v1';

const PRODUCTS = [
  { id: 'a1', title: 'Bovino - Reprodutor', price: 250000, img:'img/images (1).jpg' },
  { id: 'a2', title: 'Caprino - 6 Meses', price: 35000, img:'img/images (2).jpg' },
  { id: 'p1', title: '<PERSON><PERSON><PERSON> (saco 50kg)', price: 4500, img:'img/images (3).jpg' },
  { id: 'p2', title: 'FeijÃ£o (saco 50kg)', price: 5200, img:'img/images (4).jpg' },
  { id: 'p3', title: 'Mandioca (saco 50kg)', price: 3200, img:'img/images (5).jpg' },
  { id: 'p4', title: 'ServiÃ§o: Consultoria Agro', price: 150000, img:'img/images (6).jpg' }
];

let cart = [];
try { cart = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]'); } catch (e) { cart = []; }

function saveCart(){ 
  localStorage.setItem(STORAGE_KEY, JSON.stringify(cart)); 
  updateCartUI(); 
}

function formatCurrency(n){
  return 'AOA ' + new Intl.NumberFormat('pt-PT').format(n);
}

function renderProducts(){
  const container = document.getElementById('products');
  if(!container) return;
  container.innerHTML = '';
  PRODUCTS.forEach(p=>{
    const el = document.createElement('div');
    el.className = 'product';
    el.innerHTML = `
      <img src="${p.img}" alt="${p.title}" />
      <h5>${p.title}</h5>
      <div class="muted">CÃ³digo: ${p.id}</div>
      <div class="product-actions">
        <div><span class="price">${formatCurrency(p.price)}</span></div>
        <div>
          <button class="btn btn-outline btn-sm" data-id="${p.id}">Adicionar</button>
        </div>
      </div>
    `;
    const btn = el.querySelector('button[data-id]');
    if(btn) btn.addEventListener('click', ()=>addToCart(p.id));
    container.appendChild(el);
  });
}

function addToCart(id){
  const prod = PRODUCTS.find(p=>p.id===id);
  if(!prod) return;
  const item = cart.find(i=>i.id===id);
  if(item) item.qty++;
  else cart.push({ id:prod.id, title:prod.title, price:prod.price, img:prod.img, qty:1 });
  saveCart();
  openCart();
}

function updateCartUI(){
  const cartCountEl = document.getElementById('cartCount');
  const cartQtyEl = document.getElementById('cartQty');
  const cartTotalEl = document.getElementById('cartTotal');
  const itemsEl = document.getElementById('cartItems');

  const cartCount = cart.reduce((s,i)=>s+i.qty,0);
  if(cartCountEl) cartCountEl.textContent = cartCount;
  if(cartQtyEl) cartQtyEl.textContent = cartCount;
  const total = cart.reduce((s,i)=>s + i.price*i.qty,0);
  if(cartTotalEl) cartTotalEl.textContent = formatCurrency(total);

  if(!itemsEl) return;
  itemsEl.innerHTML = '';
  if(cart.length === 0){
    itemsEl.innerHTML = '<p class="muted">Carrinho vazio.</p>';
    return;
  }

  cart.forEach(it=>{
    const div = document.createElement('div');
    div.className = 'cart-item';
    div.innerHTML = `
      <img src="${it.img}" alt="${it.title}" />
      <div style="flex:1">
        <div style="font-weight:600">${it.title}</div>
        <div style="color:var(--muted);font-size:0.9rem">${formatCurrency(it.price)} â€¢ ${formatCurrency(it.price*it.qty)}</div>
        <div class="qty" style="margin-top:6px">
          <button class="dec">âˆ’</button>
          <div style="padding:0 8px" class="qty-count">${it.qty}</div>
          <button class="inc">+</button>
          <button class="remove" style="margin-left:8px">Remover</button>
        </div>
      </div>
    `;
    const dec = div.querySelector('.dec');
    const inc = div.querySelector('.inc');
    const rem = div.querySelector('.remove');
    if(dec) dec.addEventListener('click', ()=>changeQty(it.id, -1));
    if(inc) inc.addEventListener('click', ()=>changeQty(it.id, 1));
    if(rem) rem.addEventListener('click', ()=>removeItem(it.id));
    itemsEl.appendChild(div);
  });
}

function changeQty(id, delta){
  const it = cart.find(c=>c.id===id);
  if(!it) return;
  it.qty += delta;
  if(it.qty <= 0) cart = cart.filter(c=>c.id!==id);
  saveCart();
}

function removeItem(id){
  cart = cart.filter(c=>c.id!==id);
  saveCart();
}

function clearCart(){
  cart = [];
  saveCart();
}

function openCart(){
  const drawer = document.getElementById('cartDrawer');
  if(!drawer) return;
  drawer.classList.add('open');
  drawer.setAttribute('aria-hidden','false');
}

function closeCart(){
  const drawer = document.getElementById('cartDrawer');
  if(!drawer) return;
  drawer.classList.remove('open');
  drawer.setAttribute('aria-hidden','true');
}

/* Safe event binding (elements may not exist in other contexts) */
function safeBind(id, evt, fn){
  const el = document.getElementById(id);
  if(el) el.addEventListener(evt, fn);
}

safeBind('cartBtn','click', openCart);
safeBind('closeCart','click', closeCart);
safeBind('clearCart','click', clearCart);
safeBind('checkout','click', ()=>{
  if(cart.length===0){ alert('Carrinho vazio. Adicione itens antes de finalizar.'); return; }
  const total = cart.reduce((s,i)=>s + i.price*i.qty,0);
  alert('Compra simulada. Total: ' + formatCurrency(total) + '\nContacte-nos para finalizar pagamento e entrega.');
  clearCart();
});

const contactForm = document.getElementById('contactForm');
if(contactForm){
  contactForm.addEventListener('submit', function(e){
    e.preventDefault();
    const nome = document.getElementById('name') ? document.getElementById('name').value : '';
    alert(`Obrigado, ${nome || 'cliente'}. A sua mensagem foi recebida. NÃ³s iremos contactar.`);
    this.reset();
  });
}

/* contadores animados */
function animateCount(id, target, suffix=''){
  let count = 0;
  const el = document.getElementById(id);
  if(!el) return;
  const duration = 1200;
  const frames = 60;
  const step = Math.max(1, Math.ceil(target / frames));
  const interval = setInterval(()=>{
    count += step;
    if(count >= target){
      el.textContent = target + (suffix||'');
      clearInterval(interval);
    } else el.textContent = count + (suffix||'');
  }, Math.round(duration/frames));
}

/* Hero Slider Functionality */
class HeroSlider {
  constructor() {
    this.currentSlide = 0;
    this.slides = document.querySelectorAll('.slide');
    this.dots = document.querySelectorAll('.dot');
    this.prevBtn = document.querySelector('.prev-btn');
    this.nextBtn = document.querySelector('.next-btn');
    this.autoPlayInterval = null;
    this.autoPlayDelay = 4000; // 4 seconds

    if (this.slides.length === 0) return;

    this.init();
  }

  init() {
    // Add event listeners
    if (this.prevBtn) this.prevBtn.addEventListener('click', () => this.prevSlide());
    if (this.nextBtn) this.nextBtn.addEventListener('click', () => this.nextSlide());

    // Add dot navigation
    this.dots.forEach((dot, index) => {
      dot.addEventListener('click', () => this.goToSlide(index));
    });

    // Start autoplay
    this.startAutoPlay();

    // Pause autoplay on hover
    const slider = document.querySelector('.hero-slider');
    if (slider) {
      slider.addEventListener('mouseenter', () => this.stopAutoPlay());
      slider.addEventListener('mouseleave', () => this.startAutoPlay());
    }

    // Touch/swipe support for mobile
    this.addTouchSupport();
  }

  goToSlide(index) {
    // Remove active class from current slide and dot
    this.slides[this.currentSlide].classList.remove('active');
    this.dots[this.currentSlide].classList.remove('active');

    // Update current slide
    this.currentSlide = index;

    // Add active class to new slide and dot
    this.slides[this.currentSlide].classList.add('active');
    this.dots[this.currentSlide].classList.add('active');
  }

  nextSlide() {
    const nextIndex = (this.currentSlide + 1) % this.slides.length;
    this.goToSlide(nextIndex);
  }

  prevSlide() {
    const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
    this.goToSlide(prevIndex);
  }

  startAutoPlay() {
    this.stopAutoPlay(); // Clear any existing interval
    this.autoPlayInterval = setInterval(() => {
      this.nextSlide();
    }, this.autoPlayDelay);
  }

  stopAutoPlay() {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
  }

  addTouchSupport() {
    const slider = document.querySelector('.hero-slider');
    if (!slider) return;

    let startX = 0;
    let startY = 0;
    let endX = 0;
    let endY = 0;

    slider.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    });

    slider.addEventListener('touchend', (e) => {
      endX = e.changedTouches[0].clientX;
      endY = e.changedTouches[0].clientY;

      const deltaX = endX - startX;
      const deltaY = endY - startY;

      // Only trigger swipe if horizontal movement is greater than vertical
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX > 0) {
          this.prevSlide();
        } else {
          this.nextSlide();
        }
      }
    });
  }
}

/* InicializaÃ§Ã£o */
window.addEventListener('load', ()=>{
  // valores iniciais (edite conforme necessÃ¡rio)
  animateCount('animalCount', 350);
  animateCount('harvestCount', 120);
  const areaEl = document.getElementById('areaCount');
  if(areaEl) areaEl.textContent = '500 ha';

  renderProducts();
  updateCartUI();

  const yearEl = document.getElementById('year');
  if(yearEl) yearEl.textContent = new Date().getFullYear();

  // Initialize hero slider
  new HeroSlider();
});
